import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:balshifa/models/dose.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<void> initialize() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
    );
  }

  void onDidReceiveNotificationResponse(NotificationResponse details) {
    // معالجة الضغط على الإشعارات
    print('تم الضغط على الإشعار: ${details.payload}');
  }

  Future<void> showDoseNotification(Dose dose, String medicineName) async {
    const AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      'dose_reminder_channel',
      'تذكير بالجرعات',
      channelDescription: 'تذكير بمواعيد الجرعات الدوائية',
      importance: Importance.max,
      priority: Priority.high,
      ongoing: true, // يجعل الإشعار دائمًا
      autoCancel: false,
    );

    const NotificationDetails notificationDetails =
        NotificationDetails(android: androidNotificationDetails);

    await _notificationsPlugin.show(
      dose.id ?? 0,
      'وقت الجرعة ⏰',
      'الدواء: $medicineName\nالجرعة: ${dose.scheduledDateTime.hour}:${dose.scheduledDateTime.minute.toString().padLeft(2, '0')}',
      notificationDetails,
      payload: '${dose.id}',
    );
  }

  Future<void> cancelNotification(int notificationId) async {
    await _notificationsPlugin.cancel(notificationId);
  }

  Future<void> cancelAllNotifications() async {
    await _notificationsPlugin.cancelAll();
  }
}
