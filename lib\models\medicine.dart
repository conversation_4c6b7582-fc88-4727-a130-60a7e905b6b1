class Medicine {
  final int? id;
  final int patientId;
  final String name;
  final String type;
  final String dosage;
  final int timesPerDay;
  final int? durationDays;
  final DateTime firstDoseDateTime;

  Medicine({
    this.id,
    required this.patientId,
    required this.name,
    required this.type,
    required this.dosage,
    required this.timesPerDay,
    this.durationDays,
    required this.firstDoseDateTime,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'patient_id': patientId,
      'name': name,
      'type': type,
      'dosage': dosage,
      'times_per_day': timesPerDay,
      'duration_days': durationDays,
      'first_dose_datetime': firstDoseDateTime.toIso8601String(),
    };
  }

  factory Medicine.fromMap(Map<String, dynamic> map) {
    return Medicine(
      id: map['id'],
      patientId: map['patient_id'],
      name: map['name'],
      type: map['type'],
      dosage: map['dosage'],
      timesPerDay: map['times_per_day'],
      durationDays: map['duration_days'],
      firstDoseDateTime: DateTime.parse(map['first_dose_datetime']),
    );
  }
}
