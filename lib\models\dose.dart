class Dose {
  final int? id;
  final int medicineId;
  final DateTime scheduledDateTime;
  final DateTime? takenDateTime;
  final String status; // 'scheduled', 'taken', 'missed'

  Dose({
    this.id,
    required this.medicineId,
    required this.scheduledDateTime,
    this.takenDateTime,
    required this.status,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'medicine_id': medicineId,
      'scheduled_datetime': scheduledDateTime.toIso8601String(),
      'taken_datetime': takenDateTime?.toIso8601String(),
      'status': status,
    };
  }

  factory Dose.fromMap(Map<String, dynamic> map) {
    return Dose(
      id: map['id'],
      medicineId: map['medicine_id'],
      scheduledDateTime: DateTime.parse(map['scheduled_datetime']),
      takenDateTime: map['taken_datetime'] != null
          ? DateTime.parse(map['taken_datetime'])
          : null,
      status: map['status'],
    );
  }
}
