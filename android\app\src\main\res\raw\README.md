# مجلد الأصوات - Raw Resources

## إضافة ملف الصوت للإشعارات

لتفعيل الصوت المخصص للإشعارات، يرجى إضافة ملف صوت باسم `notification.mp3` في هذا المجلد.

### الخطوات:

1. **احصل على ملف صوت مناسب:**
   - يفضل أن يكون قصيراً (2-5 ثواني)
   - بصيغة MP3 أو WAV
   - حجم صغير لتوفير مساحة التطبيق

2. **أعد تسمية الملف:**
   - اسم الملف يجب أن يكون: `notification.mp3`
   - تأكد من عدم وجود مسافات أو أحرف خاصة

3. **ضع الملف في هذا المجلد:**
   ```
   android/app/src/main/res/raw/notification.mp3
   ```

4. **أعد بناء التطبيق:**
   ```bash
   flutter clean
   flutter build apk
   ```

### ملاحظات:

- إذا لم تضع ملف الصوت، سيستخدم التطبيق الصوت الافتراضي للنظام
- تأكد من أن الملف ليس كبير الحجم لتجنب زيادة حجم التطبيق
- يمكنك استخدام أصوات مجانية من مواقع مثل:
  - Freesound.org
  - Zapsplat.com
  - أو تسجيل صوت مخصص

### أمثلة على أسماء ملفات صوتية مقبولة:
- `notification.mp3` ✅
- `notification.wav` ✅
- `alert_sound.mp3` (يتطلب تعديل الكود) ✅

### أسماء غير مقبولة:
- `notification sound.mp3` ❌ (يحتوي على مسافة)
- `إشعار.mp3` ❌ (أحرف عربية)
- `notification-sound.mp3` ❌ (يحتوي على شرطة)
